<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service;

use Google\Client;

/**
 * Service definition for VersionHistory (v1).
 *
 * <p>
 * Version History API - Prod</p>
 *
 * <p>
 * For more information about this service, see the API
 * <a href="https://developers.chrome.com/versionhistory" target="_blank">Documentation</a>
 * </p>
 *
 * <AUTHOR> Inc.
 */
class VersionHistory extends \Google\Service
{


  public $platforms;
  public $platforms_channels;
  public $platforms_channels_versions;
  public $platforms_channels_versions_releases;
  public $rootUrlTemplate;

  /**
   * Constructs the internal representation of the VersionHistory service.
   *
   * @param Client|array $clientOrConfig The client used to deliver requests, or a
   *                                     config array to pass to a new Client instance.
   * @param string $rootUrl The root URL used for requests to the service.
   */
  public function __construct($clientOrConfig = [], $rootUrl = null)
  {
    parent::__construct($clientOrConfig);
    $this->rootUrl = $rootUrl ?: 'https://versionhistory.googleapis.com/';
    $this->rootUrlTemplate = $rootUrl ?: 'https://versionhistory.UNIVERSE_DOMAIN/';
    $this->servicePath = '';
    $this->batchPath = 'batch';
    $this->version = 'v1';
    $this->serviceName = 'versionhistory';

    $this->platforms = new VersionHistory\Resource\Platforms(
        $this,
        $this->serviceName,
        'platforms',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/{+parent}/platforms',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->platforms_channels = new VersionHistory\Resource\PlatformsChannels(
        $this,
        $this->serviceName,
        'channels',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/{+parent}/channels',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->platforms_channels_versions = new VersionHistory\Resource\PlatformsChannelsVersions(
        $this,
        $this->serviceName,
        'versions',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/{+parent}/versions',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'orderBy' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->platforms_channels_versions_releases = new VersionHistory\Resource\PlatformsChannelsVersionsReleases(
        $this,
        $this->serviceName,
        'releases',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/{+parent}/releases',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'orderBy' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(VersionHistory::class, 'Google_Service_VersionHistory');
