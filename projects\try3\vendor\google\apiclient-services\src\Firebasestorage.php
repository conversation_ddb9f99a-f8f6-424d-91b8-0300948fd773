<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service;

use Google\Client;

/**
 * Service definition for Firebasestorage (v1beta).
 *
 * <p>
 * The Cloud Storage for Firebase API enables programmatic management of Cloud
 * Storage buckets for use in Firebase projects</p>
 *
 * <p>
 * For more information about this service, see the API
 * <a href="https://firebase.google.com/docs/storage" target="_blank">Documentation</a>
 * </p>
 *
 * <AUTHOR> Inc.
 */
class Firebasestorage extends \Google\Service
{
  /** See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account.. */
  const CLOUD_PLATFORM =
      "https://www.googleapis.com/auth/cloud-platform";
  /** View and administer all your Firebase data and settings. */
  const FIREBASE =
      "https://www.googleapis.com/auth/firebase";

  public $projects;
  public $projects_buckets;
  public $projects_defaultBucket;
  public $rootUrlTemplate;

  /**
   * Constructs the internal representation of the Firebasestorage service.
   *
   * @param Client|array $clientOrConfig The client used to deliver requests, or a
   *                                     config array to pass to a new Client instance.
   * @param string $rootUrl The root URL used for requests to the service.
   */
  public function __construct($clientOrConfig = [], $rootUrl = null)
  {
    parent::__construct($clientOrConfig);
    $this->rootUrl = $rootUrl ?: 'https://firebasestorage.googleapis.com/';
    $this->rootUrlTemplate = $rootUrl ?: 'https://firebasestorage.UNIVERSE_DOMAIN/';
    $this->servicePath = '';
    $this->batchPath = 'batch';
    $this->version = 'v1beta';
    $this->serviceName = 'firebasestorage';

    $this->projects = new Firebasestorage\Resource\Projects(
        $this,
        $this->serviceName,
        'projects',
        [
          'methods' => [
            'deleteDefaultBucket' => [
              'path' => 'v1beta/{+name}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'getDefaultBucket' => [
              'path' => 'v1beta/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_buckets = new Firebasestorage\Resource\ProjectsBuckets(
        $this,
        $this->serviceName,
        'buckets',
        [
          'methods' => [
            'addFirebase' => [
              'path' => 'v1beta/{+bucket}:addFirebase',
              'httpMethod' => 'POST',
              'parameters' => [
                'bucket' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1beta/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1beta/{+parent}/buckets',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'removeFirebase' => [
              'path' => 'v1beta/{+bucket}:removeFirebase',
              'httpMethod' => 'POST',
              'parameters' => [
                'bucket' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_defaultBucket = new Firebasestorage\Resource\ProjectsDefaultBucket(
        $this,
        $this->serviceName,
        'defaultBucket',
        [
          'methods' => [
            'create' => [
              'path' => 'v1beta/{+parent}/defaultBucket',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Firebasestorage::class, 'Google_Service_Firebasestorage');
