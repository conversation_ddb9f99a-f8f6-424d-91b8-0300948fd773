<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service;

use Google\Client;

/**
 * Service definition for Fcmdata (v1beta1).
 *
 * <p>
 * Provides additional information about Firebase Cloud Messaging (FCM) message
 * sends and deliveries.</p>
 *
 * <p>
 * For more information about this service, see the API
 * <a href="https://firebase.google.com/docs/cloud-messaging" target="_blank">Documentation</a>
 * </p>
 *
 * <AUTHOR> Inc.
 */
class Fcmdata extends \Google\Service
{
  /** See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account.. */
  const CLOUD_PLATFORM =
      "https://www.googleapis.com/auth/cloud-platform";

  public $projects_androidApps_deliveryData;
  public $rootUrlTemplate;

  /**
   * Constructs the internal representation of the Fcmdata service.
   *
   * @param Client|array $clientOrConfig The client used to deliver requests, or a
   *                                     config array to pass to a new Client instance.
   * @param string $rootUrl The root URL used for requests to the service.
   */
  public function __construct($clientOrConfig = [], $rootUrl = null)
  {
    parent::__construct($clientOrConfig);
    $this->rootUrl = $rootUrl ?: 'https://fcmdata.googleapis.com/';
    $this->rootUrlTemplate = $rootUrl ?: 'https://fcmdata.UNIVERSE_DOMAIN/';
    $this->servicePath = '';
    $this->batchPath = 'batch';
    $this->version = 'v1beta1';
    $this->serviceName = 'fcmdata';

    $this->projects_androidApps_deliveryData = new Fcmdata\Resource\ProjectsAndroidAppsDeliveryData(
        $this,
        $this->serviceName,
        'deliveryData',
        [
          'methods' => [
            'list' => [
              'path' => 'v1beta1/{+parent}/deliveryData',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Fcmdata::class, 'Google_Service_Fcmdata');
