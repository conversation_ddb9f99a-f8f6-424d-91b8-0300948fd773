<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service;

use Google\Client;

/**
 * Service definition for Cloudchannel (v1).
 *
 * <p>
 * The Cloud Channel API enables Google Cloud partners to have a single unified
 * resale platform and APIs across all of Google Cloud including GCP, Workspace,
 * Maps and Chrome.</p>
 *
 * <p>
 * For more information about this service, see the API
 * <a href="https://cloud.google.com/channel" target="_blank">Documentation</a>
 * </p>
 *
 * <AUTHOR> Inc.
 */
class Cloudchannel extends \Google\Service
{
  /** Manage users on your domain. */
  const APPS_ORDER =
      "https://www.googleapis.com/auth/apps.order";
  /** View usage reports for your G Suite domain. */
  const APPS_REPORTS_USAGE_READONLY =
      "https://www.googleapis.com/auth/apps.reports.usage.readonly";

  public $accounts;
  public $accounts_channelPartnerLinks;
  public $accounts_channelPartnerLinks_channelPartnerRepricingConfigs;
  public $accounts_channelPartnerLinks_customers;
  public $accounts_customers;
  public $accounts_customers_customerRepricingConfigs;
  public $accounts_customers_entitlements;
  public $accounts_offers;
  public $accounts_reportJobs;
  public $accounts_reports;
  public $accounts_skuGroups;
  public $accounts_skuGroups_billableSkus;
  public $integrators;
  public $operations;
  public $products;
  public $products_skus;
  public $rootUrlTemplate;

  /**
   * Constructs the internal representation of the Cloudchannel service.
   *
   * @param Client|array $clientOrConfig The client used to deliver requests, or a
   *                                     config array to pass to a new Client instance.
   * @param string $rootUrl The root URL used for requests to the service.
   */
  public function __construct($clientOrConfig = [], $rootUrl = null)
  {
    parent::__construct($clientOrConfig);
    $this->rootUrl = $rootUrl ?: 'https://cloudchannel.googleapis.com/';
    $this->rootUrlTemplate = $rootUrl ?: 'https://cloudchannel.UNIVERSE_DOMAIN/';
    $this->servicePath = '';
    $this->batchPath = 'batch';
    $this->version = 'v1';
    $this->serviceName = 'cloudchannel';

    $this->accounts = new Cloudchannel\Resource\Accounts(
        $this,
        $this->serviceName,
        'accounts',
        [
          'methods' => [
            'checkCloudIdentityAccountsExist' => [
              'path' => 'v1/{+parent}:checkCloudIdentityAccountsExist',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'listSubscribers' => [
              'path' => 'v1/{+account}:listSubscribers',
              'httpMethod' => 'GET',
              'parameters' => [
                'account' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'integrator' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'listTransferableOffers' => [
              'path' => 'v1/{+parent}:listTransferableOffers',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'listTransferableSkus' => [
              'path' => 'v1/{+parent}:listTransferableSkus',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'register' => [
              'path' => 'v1/{+account}:register',
              'httpMethod' => 'POST',
              'parameters' => [
                'account' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'unregister' => [
              'path' => 'v1/{+account}:unregister',
              'httpMethod' => 'POST',
              'parameters' => [
                'account' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_channelPartnerLinks = new Cloudchannel\Resource\AccountsChannelPartnerLinks(
        $this,
        $this->serviceName,
        'channelPartnerLinks',
        [
          'methods' => [
            'create' => [
              'path' => 'v1/{+parent}/channelPartnerLinks',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'view' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/channelPartnerLinks',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'view' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'patch' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'PATCH',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_channelPartnerLinks_channelPartnerRepricingConfigs = new Cloudchannel\Resource\AccountsChannelPartnerLinksChannelPartnerRepricingConfigs(
        $this,
        $this->serviceName,
        'channelPartnerRepricingConfigs',
        [
          'methods' => [
            'create' => [
              'path' => 'v1/{+parent}/channelPartnerRepricingConfigs',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'delete' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/channelPartnerRepricingConfigs',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'patch' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'PATCH',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_channelPartnerLinks_customers = new Cloudchannel\Resource\AccountsChannelPartnerLinksCustomers(
        $this,
        $this->serviceName,
        'customers',
        [
          'methods' => [
            'create' => [
              'path' => 'v1/{+parent}/customers',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'delete' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'import' => [
              'path' => 'v1/{+parent}/customers:import',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/customers',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'patch' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'PATCH',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'updateMask' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_customers = new Cloudchannel\Resource\AccountsCustomers(
        $this,
        $this->serviceName,
        'customers',
        [
          'methods' => [
            'create' => [
              'path' => 'v1/{+parent}/customers',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'delete' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'import' => [
              'path' => 'v1/{+parent}/customers:import',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/customers',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'listPurchasableOffers' => [
              'path' => 'v1/{+customer}:listPurchasableOffers',
              'httpMethod' => 'GET',
              'parameters' => [
                'customer' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'changeOfferPurchase.billingAccount' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'changeOfferPurchase.entitlement' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'changeOfferPurchase.newSku' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'createEntitlementPurchase.billingAccount' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'createEntitlementPurchase.sku' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'languageCode' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'listPurchasableSkus' => [
              'path' => 'v1/{+customer}:listPurchasableSkus',
              'httpMethod' => 'GET',
              'parameters' => [
                'customer' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'changeOfferPurchase.changeType' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'changeOfferPurchase.entitlement' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'createEntitlementPurchase.product' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'languageCode' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'patch' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'PATCH',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'updateMask' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'provisionCloudIdentity' => [
              'path' => 'v1/{+customer}:provisionCloudIdentity',
              'httpMethod' => 'POST',
              'parameters' => [
                'customer' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'queryEligibleBillingAccounts' => [
              'path' => 'v1/{+customer}:queryEligibleBillingAccounts',
              'httpMethod' => 'GET',
              'parameters' => [
                'customer' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'skus' => [
                  'location' => 'query',
                  'type' => 'string',
                  'repeated' => true,
                ],
              ],
            ],'transferEntitlements' => [
              'path' => 'v1/{+parent}:transferEntitlements',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'transferEntitlementsToGoogle' => [
              'path' => 'v1/{+parent}:transferEntitlementsToGoogle',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_customers_customerRepricingConfigs = new Cloudchannel\Resource\AccountsCustomersCustomerRepricingConfigs(
        $this,
        $this->serviceName,
        'customerRepricingConfigs',
        [
          'methods' => [
            'create' => [
              'path' => 'v1/{+parent}/customerRepricingConfigs',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'delete' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/customerRepricingConfigs',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'patch' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'PATCH',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_customers_entitlements = new Cloudchannel\Resource\AccountsCustomersEntitlements(
        $this,
        $this->serviceName,
        'entitlements',
        [
          'methods' => [
            'activate' => [
              'path' => 'v1/{+name}:activate',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'cancel' => [
              'path' => 'v1/{+name}:cancel',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'changeOffer' => [
              'path' => 'v1/{+name}:changeOffer',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'changeParameters' => [
              'path' => 'v1/{+name}:changeParameters',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'changeRenewalSettings' => [
              'path' => 'v1/{+name}:changeRenewalSettings',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'create' => [
              'path' => 'v1/{+parent}/entitlements',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/entitlements',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'listEntitlementChanges' => [
              'path' => 'v1/{+parent}:listEntitlementChanges',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'lookupOffer' => [
              'path' => 'v1/{+entitlement}:lookupOffer',
              'httpMethod' => 'GET',
              'parameters' => [
                'entitlement' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'startPaidService' => [
              'path' => 'v1/{+name}:startPaidService',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'suspend' => [
              'path' => 'v1/{+name}:suspend',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_offers = new Cloudchannel\Resource\AccountsOffers(
        $this,
        $this->serviceName,
        'offers',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/{+parent}/offers',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'languageCode' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'showFutureOffers' => [
                  'location' => 'query',
                  'type' => 'boolean',
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_reportJobs = new Cloudchannel\Resource\AccountsReportJobs(
        $this,
        $this->serviceName,
        'reportJobs',
        [
          'methods' => [
            'fetchReportResults' => [
              'path' => 'v1/{+reportJob}:fetchReportResults',
              'httpMethod' => 'POST',
              'parameters' => [
                'reportJob' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_reports = new Cloudchannel\Resource\AccountsReports(
        $this,
        $this->serviceName,
        'reports',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/{+parent}/reports',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'languageCode' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'run' => [
              'path' => 'v1/{+name}:run',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_skuGroups = new Cloudchannel\Resource\AccountsSkuGroups(
        $this,
        $this->serviceName,
        'skuGroups',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/{+parent}/skuGroups',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->accounts_skuGroups_billableSkus = new Cloudchannel\Resource\AccountsSkuGroupsBillableSkus(
        $this,
        $this->serviceName,
        'billableSkus',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/{+parent}/billableSkus',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->integrators = new Cloudchannel\Resource\Integrators(
        $this,
        $this->serviceName,
        'integrators',
        [
          'methods' => [
            'listSubscribers' => [
              'path' => 'v1/{+integrator}:listSubscribers',
              'httpMethod' => 'GET',
              'parameters' => [
                'integrator' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'account' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'registerSubscriber' => [
              'path' => 'v1/{+integrator}:registerSubscriber',
              'httpMethod' => 'POST',
              'parameters' => [
                'integrator' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'unregisterSubscriber' => [
              'path' => 'v1/{+integrator}:unregisterSubscriber',
              'httpMethod' => 'POST',
              'parameters' => [
                'integrator' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->operations = new Cloudchannel\Resource\Operations(
        $this,
        $this->serviceName,
        'operations',
        [
          'methods' => [
            'cancel' => [
              'path' => 'v1/{+name}:cancel',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'delete' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->products = new Cloudchannel\Resource\Products(
        $this,
        $this->serviceName,
        'products',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/products',
              'httpMethod' => 'GET',
              'parameters' => [
                'account' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'languageCode' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->products_skus = new Cloudchannel\Resource\ProductsSkus(
        $this,
        $this->serviceName,
        'skus',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/{+parent}/skus',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'account' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'languageCode' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Cloudchannel::class, 'Google_Service_Cloudchannel');
