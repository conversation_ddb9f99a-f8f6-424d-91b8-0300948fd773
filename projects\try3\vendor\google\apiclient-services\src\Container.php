<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service;

use Google\Client;

/**
 * Service definition for Container (v1).
 *
 * <p>
 * Builds and manages container-based applications, powered by the open source
 * Kubernetes technology.</p>
 *
 * <p>
 * For more information about this service, see the API
 * <a href="https://cloud.google.com/kubernetes-engine/docs/" target="_blank">Documentation</a>
 * </p>
 *
 * <AUTHOR> Inc.
 */
class Container extends \Google\Service
{
  /** See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account.. */
  const CLOUD_PLATFORM =
      "https://www.googleapis.com/auth/cloud-platform";

  public $projects_aggregated_usableSubnetworks;
  public $projects_locations;
  public $projects_locations_clusters;
  public $projects_locations_clusters_nodePools;
  public $projects_locations_clusters_well_known;
  public $projects_locations_operations;
  public $projects_zones;
  public $projects_zones_clusters;
  public $projects_zones_clusters_nodePools;
  public $projects_zones_operations;
  public $rootUrlTemplate;

  /**
   * Constructs the internal representation of the Container service.
   *
   * @param Client|array $clientOrConfig The client used to deliver requests, or a
   *                                     config array to pass to a new Client instance.
   * @param string $rootUrl The root URL used for requests to the service.
   */
  public function __construct($clientOrConfig = [], $rootUrl = null)
  {
    parent::__construct($clientOrConfig);
    $this->rootUrl = $rootUrl ?: 'https://container.googleapis.com/';
    $this->rootUrlTemplate = $rootUrl ?: 'https://container.UNIVERSE_DOMAIN/';
    $this->servicePath = '';
    $this->batchPath = 'batch';
    $this->version = 'v1';
    $this->serviceName = 'container';

    $this->projects_aggregated_usableSubnetworks = new Container\Resource\ProjectsAggregatedUsableSubnetworks(
        $this,
        $this->serviceName,
        'usableSubnetworks',
        [
          'methods' => [
            'list' => [
              'path' => 'v1/{+parent}/aggregated/usableSubnetworks',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_locations = new Container\Resource\ProjectsLocations(
        $this,
        $this->serviceName,
        'locations',
        [
          'methods' => [
            'getServerConfig' => [
              'path' => 'v1/{+name}/serverConfig',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'projectId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'zone' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_locations_clusters = new Container\Resource\ProjectsLocationsClusters(
        $this,
        $this->serviceName,
        'clusters',
        [
          'methods' => [
            'checkAutopilotCompatibility' => [
              'path' => 'v1/{+name}:checkAutopilotCompatibility',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'completeIpRotation' => [
              'path' => 'v1/{+name}:completeIpRotation',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'create' => [
              'path' => 'v1/{+parent}/clusters',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'delete' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'projectId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'zone' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'projectId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'zone' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'getJwks' => [
              'path' => 'v1/{+parent}/jwks',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/clusters',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'projectId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'zone' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'setAddons' => [
              'path' => 'v1/{+name}:setAddons',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setLegacyAbac' => [
              'path' => 'v1/{+name}:setLegacyAbac',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setLocations' => [
              'path' => 'v1/{+name}:setLocations',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setLogging' => [
              'path' => 'v1/{+name}:setLogging',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setMaintenancePolicy' => [
              'path' => 'v1/{+name}:setMaintenancePolicy',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setMasterAuth' => [
              'path' => 'v1/{+name}:setMasterAuth',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setMonitoring' => [
              'path' => 'v1/{+name}:setMonitoring',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setNetworkPolicy' => [
              'path' => 'v1/{+name}:setNetworkPolicy',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setResourceLabels' => [
              'path' => 'v1/{+name}:setResourceLabels',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'startIpRotation' => [
              'path' => 'v1/{+name}:startIpRotation',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'update' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'PUT',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'updateMaster' => [
              'path' => 'v1/{+name}:updateMaster',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_locations_clusters_nodePools = new Container\Resource\ProjectsLocationsClustersNodePools(
        $this,
        $this->serviceName,
        'nodePools',
        [
          'methods' => [
            'completeUpgrade' => [
              'path' => 'v1/{+name}:completeUpgrade',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'create' => [
              'path' => 'v1/{+parent}/nodePools',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'delete' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'nodePoolId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'projectId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'zone' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'nodePoolId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'projectId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'zone' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/nodePools',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'projectId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'zone' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'rollback' => [
              'path' => 'v1/{+name}:rollback',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setAutoscaling' => [
              'path' => 'v1/{+name}:setAutoscaling',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setManagement' => [
              'path' => 'v1/{+name}:setManagement',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setSize' => [
              'path' => 'v1/{+name}:setSize',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'update' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'PUT',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_locations_clusters_well_known = new Container\Resource\ProjectsLocationsClustersWellKnown(
        $this,
        $this->serviceName,
        'well_known',
        [
          'methods' => [
            'getOpenid-configuration' => [
              'path' => 'v1/{+parent}/.well-known/openid-configuration',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_locations_operations = new Container\Resource\ProjectsLocationsOperations(
        $this,
        $this->serviceName,
        'operations',
        [
          'methods' => [
            'cancel' => [
              'path' => 'v1/{+name}:cancel',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'operationId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'projectId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'zone' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/operations',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'projectId' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'zone' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_zones = new Container\Resource\ProjectsZones(
        $this,
        $this->serviceName,
        'zones',
        [
          'methods' => [
            'getServerconfig' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/serverconfig',
              'httpMethod' => 'GET',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'name' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_zones_clusters = new Container\Resource\ProjectsZonesClusters(
        $this,
        $this->serviceName,
        'clusters',
        [
          'methods' => [
            'addons' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/addons',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'completeIpRotation' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}:completeIpRotation',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'create' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'delete' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'name' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'get' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}',
              'httpMethod' => 'GET',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'name' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'legacyAbac' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/legacyAbac',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters',
              'httpMethod' => 'GET',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'parent' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'locations' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/locations',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'logging' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/logging',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'master' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/master',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'monitoring' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/monitoring',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'resourceLabels' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/resourceLabels',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setMaintenancePolicy' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}:setMaintenancePolicy',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setMasterAuth' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}:setMasterAuth',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setNetworkPolicy' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}:setNetworkPolicy',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'startIpRotation' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}:startIpRotation',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'update' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}',
              'httpMethod' => 'PUT',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_zones_clusters_nodePools = new Container\Resource\ProjectsZonesClustersNodePools(
        $this,
        $this->serviceName,
        'nodePools',
        [
          'methods' => [
            'autoscaling' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/nodePools/{nodePoolId}/autoscaling',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'nodePoolId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'create' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/nodePools',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'delete' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/nodePools/{nodePoolId}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'nodePoolId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'name' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'get' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/nodePools/{nodePoolId}',
              'httpMethod' => 'GET',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'nodePoolId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'name' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'list' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/nodePools',
              'httpMethod' => 'GET',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'parent' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'rollback' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/nodePools/{nodePoolId}:rollback',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'nodePoolId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setManagement' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/nodePools/{nodePoolId}/setManagement',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'nodePoolId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'setSize' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/nodePools/{nodePoolId}/setSize',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'nodePoolId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'update' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/clusters/{clusterId}/nodePools/{nodePoolId}/update',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'clusterId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'nodePoolId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_zones_operations = new Container\Resource\ProjectsZonesOperations(
        $this,
        $this->serviceName,
        'operations',
        [
          'methods' => [
            'cancel' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/operations/{operationId}:cancel',
              'httpMethod' => 'POST',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'operationId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/operations/{operationId}',
              'httpMethod' => 'GET',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'operationId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'name' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'list' => [
              'path' => 'v1/projects/{projectId}/zones/{zone}/operations',
              'httpMethod' => 'GET',
              'parameters' => [
                'projectId' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zone' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'parent' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Container::class, 'Google_Service_Container');
