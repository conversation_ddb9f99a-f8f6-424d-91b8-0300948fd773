<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service;

use Google\Client;

/**
 * Service definition for Pollen (v1).
 *
 * <p>
 * The Pollen API.</p>
 *
 * <p>
 * For more information about this service, see the API
 * <a href="https://developers.google.com/maps/documentation/pollen" target="_blank">Documentation</a>
 * </p>
 *
 * <AUTHOR> Inc.
 */
class Pollen extends \Google\Service
{
  /** See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account.. */
  const CLOUD_PLATFORM =
      "https://www.googleapis.com/auth/cloud-platform";

  public $forecast;
  public $mapTypes_heatmapTiles;
  public $rootUrlTemplate;

  /**
   * Constructs the internal representation of the Pollen service.
   *
   * @param Client|array $clientOrConfig The client used to deliver requests, or a
   *                                     config array to pass to a new Client instance.
   * @param string $rootUrl The root URL used for requests to the service.
   */
  public function __construct($clientOrConfig = [], $rootUrl = null)
  {
    parent::__construct($clientOrConfig);
    $this->rootUrl = $rootUrl ?: 'https://pollen.googleapis.com/';
    $this->rootUrlTemplate = $rootUrl ?: 'https://pollen.UNIVERSE_DOMAIN/';
    $this->servicePath = '';
    $this->batchPath = 'batch';
    $this->version = 'v1';
    $this->serviceName = 'pollen';

    $this->forecast = new Pollen\Resource\Forecast(
        $this,
        $this->serviceName,
        'forecast',
        [
          'methods' => [
            'lookup' => [
              'path' => 'v1/forecast:lookup',
              'httpMethod' => 'GET',
              'parameters' => [
                'days' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'languageCode' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'location.latitude' => [
                  'location' => 'query',
                  'type' => 'number',
                ],
                'location.longitude' => [
                  'location' => 'query',
                  'type' => 'number',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'plantsDescription' => [
                  'location' => 'query',
                  'type' => 'boolean',
                ],
              ],
            ],
          ]
        ]
    );
    $this->mapTypes_heatmapTiles = new Pollen\Resource\MapTypesHeatmapTiles(
        $this,
        $this->serviceName,
        'heatmapTiles',
        [
          'methods' => [
            'lookupHeatmapTile' => [
              'path' => 'v1/mapTypes/{mapType}/heatmapTiles/{zoom}/{x}/{y}',
              'httpMethod' => 'GET',
              'parameters' => [
                'mapType' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'zoom' => [
                  'location' => 'path',
                  'type' => 'integer',
                  'required' => true,
                ],
                'x' => [
                  'location' => 'path',
                  'type' => 'integer',
                  'required' => true,
                ],
                'y' => [
                  'location' => 'path',
                  'type' => 'integer',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Pollen::class, 'Google_Service_Pollen');
