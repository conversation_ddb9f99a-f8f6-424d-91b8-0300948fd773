Google PHP API Client Services
==============================

[Reference Documentation](https://googleapis.github.io/google-api-php-client-services)

**NOTE**: please check to see if the package you'd like to install is available in our
list of [Google cloud packages](https://cloud.google.com/php/docs/reference) first, as
these are the recommended libraries.

## Requirements

[Google API PHP Client](https://github.com/googleapis/google-api-php-client/releases)

## Usage

This library is automatically updated daily with new API changes, and tagged weekly.
It is installed as part of the
[Google API PHP Client](https://github.com/googleapis/google-api-php-client/releases)
library via Composer, which will pull down the most recent tag.
