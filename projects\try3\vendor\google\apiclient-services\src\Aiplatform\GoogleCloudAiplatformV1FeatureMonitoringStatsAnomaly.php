<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class GoogleCloudAiplatformV1FeatureMonitoringStatsAnomaly extends \Google\Model
{
  protected $featureStatsAnomalyType = GoogleCloudAiplatformV1FeatureStatsAnomaly::class;
  protected $featureStatsAnomalyDataType = '';
  /**
   * @var string
   */
  public $objective;

  /**
   * @param GoogleCloudAiplatformV1FeatureStatsAnomaly
   */
  public function setFeatureStatsAnomaly(GoogleCloudAiplatformV1FeatureStatsAnomaly $featureStatsAnomaly)
  {
    $this->featureStatsAnomaly = $featureStatsAnomaly;
  }
  /**
   * @return GoogleCloudAiplatformV1FeatureStatsAnomaly
   */
  public function getFeatureStatsAnomaly()
  {
    return $this->featureStatsAnomaly;
  }
  /**
   * @param string
   */
  public function setObjective($objective)
  {
    $this->objective = $objective;
  }
  /**
   * @return string
   */
  public function getObjective()
  {
    return $this->objective;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAiplatformV1FeatureMonitoringStatsAnomaly::class, 'Google_Service_Aiplatform_GoogleCloudAiplatformV1FeatureMonitoringStatsAnomaly');
