<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class GoogleCloudAiplatformV1ImportDataConfig extends \Google\Model
{
  /**
   * @var string[]
   */
  public $annotationLabels;
  /**
   * @var string[]
   */
  public $dataItemLabels;
  protected $gcsSourceType = GoogleCloudAiplatformV1GcsSource::class;
  protected $gcsSourceDataType = '';
  /**
   * @var string
   */
  public $importSchemaUri;

  /**
   * @param string[]
   */
  public function setAnnotationLabels($annotationLabels)
  {
    $this->annotationLabels = $annotationLabels;
  }
  /**
   * @return string[]
   */
  public function getAnnotationLabels()
  {
    return $this->annotationLabels;
  }
  /**
   * @param string[]
   */
  public function setDataItemLabels($dataItemLabels)
  {
    $this->dataItemLabels = $dataItemLabels;
  }
  /**
   * @return string[]
   */
  public function getDataItemLabels()
  {
    return $this->dataItemLabels;
  }
  /**
   * @param GoogleCloudAiplatformV1GcsSource
   */
  public function setGcsSource(GoogleCloudAiplatformV1GcsSource $gcsSource)
  {
    $this->gcsSource = $gcsSource;
  }
  /**
   * @return GoogleCloudAiplatformV1GcsSource
   */
  public function getGcsSource()
  {
    return $this->gcsSource;
  }
  /**
   * @param string
   */
  public function setImportSchemaUri($importSchemaUri)
  {
    $this->importSchemaUri = $importSchemaUri;
  }
  /**
   * @return string
   */
  public function getImportSchemaUri()
  {
    return $this->importSchemaUri;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAiplatformV1ImportDataConfig::class, 'Google_Service_Aiplatform_GoogleCloudAiplatformV1ImportDataConfig');
