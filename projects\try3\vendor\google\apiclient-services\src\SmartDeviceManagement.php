<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service;

use Google\Client;

/**
 * Service definition for SmartDeviceManagement (v1).
 *
 * <p>
 * Allow select enterprise partners to access, control, and manage Google and
 * Nest devices programmatically.</p>
 *
 * <p>
 * For more information about this service, see the API
 * <a href="https://developers.google.com/nest/device-access" target="_blank">Documentation</a>
 * </p>
 *
 * <AUTHOR> Inc.
 */
class SmartDeviceManagement extends \Google\Service
{
  /** See and/or control the devices that you selected. */
  const SDM_SERVICE =
      "https://www.googleapis.com/auth/sdm.service";

  public $enterprises_devices;
  public $enterprises_structures;
  public $enterprises_structures_rooms;
  public $rootUrlTemplate;

  /**
   * Constructs the internal representation of the SmartDeviceManagement
   * service.
   *
   * @param Client|array $clientOrConfig The client used to deliver requests, or a
   *                                     config array to pass to a new Client instance.
   * @param string $rootUrl The root URL used for requests to the service.
   */
  public function __construct($clientOrConfig = [], $rootUrl = null)
  {
    parent::__construct($clientOrConfig);
    $this->rootUrl = $rootUrl ?: 'https://smartdevicemanagement.googleapis.com/';
    $this->rootUrlTemplate = $rootUrl ?: 'https://smartdevicemanagement.UNIVERSE_DOMAIN/';
    $this->servicePath = '';
    $this->batchPath = 'batch';
    $this->version = 'v1';
    $this->serviceName = 'smartdevicemanagement';

    $this->enterprises_devices = new SmartDeviceManagement\Resource\EnterprisesDevices(
        $this,
        $this->serviceName,
        'devices',
        [
          'methods' => [
            'executeCommand' => [
              'path' => 'v1/{+name}:executeCommand',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/devices',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->enterprises_structures = new SmartDeviceManagement\Resource\EnterprisesStructures(
        $this,
        $this->serviceName,
        'structures',
        [
          'methods' => [
            'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/structures',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->enterprises_structures_rooms = new SmartDeviceManagement\Resource\EnterprisesStructuresRooms(
        $this,
        $this->serviceName,
        'rooms',
        [
          'methods' => [
            'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/rooms',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(SmartDeviceManagement::class, 'Google_Service_SmartDeviceManagement');
