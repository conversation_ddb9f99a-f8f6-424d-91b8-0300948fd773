<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class GoogleCloudAiplatformV1FunctionDeclaration extends \Google\Model
{
  /**
   * @var string
   */
  public $description;
  /**
   * @var string
   */
  public $name;
  protected $parametersType = GoogleCloudAiplatformV1Schema::class;
  protected $parametersDataType = '';
  protected $responseType = GoogleCloudAiplatformV1Schema::class;
  protected $responseDataType = '';

  /**
   * @param string
   */
  public function setDescription($description)
  {
    $this->description = $description;
  }
  /**
   * @return string
   */
  public function getDescription()
  {
    return $this->description;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param GoogleCloudAiplatformV1Schema
   */
  public function setParameters(GoogleCloudAiplatformV1Schema $parameters)
  {
    $this->parameters = $parameters;
  }
  /**
   * @return GoogleCloudAiplatformV1Schema
   */
  public function getParameters()
  {
    return $this->parameters;
  }
  /**
   * @param GoogleCloudAiplatformV1Schema
   */
  public function setResponse(GoogleCloudAiplatformV1Schema $response)
  {
    $this->response = $response;
  }
  /**
   * @return GoogleCloudAiplatformV1Schema
   */
  public function getResponse()
  {
    return $this->response;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAiplatformV1FunctionDeclaration::class, 'Google_Service_Aiplatform_GoogleCloudAiplatformV1FunctionDeclaration');
