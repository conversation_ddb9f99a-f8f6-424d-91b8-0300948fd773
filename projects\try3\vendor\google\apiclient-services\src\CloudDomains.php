<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service;

use Google\Client;

/**
 * Service definition for CloudDomains (v1).
 *
 * <p>
 * Enables management and configuration of domain names.</p>
 *
 * <p>
 * For more information about this service, see the API
 * <a href="https://cloud.google.com/domains/" target="_blank">Documentation</a>
 * </p>
 *
 * <AUTHOR> Inc.
 */
class CloudDomains extends \Google\Service
{
  /** See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account.. */
  const CLOUD_PLATFORM =
      "https://www.googleapis.com/auth/cloud-platform";

  public $projects_locations;
  public $projects_locations_operations;
  public $projects_locations_registrations;
  public $rootUrlTemplate;

  /**
   * Constructs the internal representation of the CloudDomains service.
   *
   * @param Client|array $clientOrConfig The client used to deliver requests, or a
   *                                     config array to pass to a new Client instance.
   * @param string $rootUrl The root URL used for requests to the service.
   */
  public function __construct($clientOrConfig = [], $rootUrl = null)
  {
    parent::__construct($clientOrConfig);
    $this->rootUrl = $rootUrl ?: 'https://domains.googleapis.com/';
    $this->rootUrlTemplate = $rootUrl ?: 'https://domains.UNIVERSE_DOMAIN/';
    $this->servicePath = '';
    $this->batchPath = 'batch';
    $this->version = 'v1';
    $this->serviceName = 'domains';

    $this->projects_locations = new CloudDomains\Resource\ProjectsLocations(
        $this,
        $this->serviceName,
        'locations',
        [
          'methods' => [
            'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+name}/locations',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_locations_operations = new CloudDomains\Resource\ProjectsLocationsOperations(
        $this,
        $this->serviceName,
        'operations',
        [
          'methods' => [
            'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+name}/operations',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],
          ]
        ]
    );
    $this->projects_locations_registrations = new CloudDomains\Resource\ProjectsLocationsRegistrations(
        $this,
        $this->serviceName,
        'registrations',
        [
          'methods' => [
            'configureContactSettings' => [
              'path' => 'v1/{+registration}:configureContactSettings',
              'httpMethod' => 'POST',
              'parameters' => [
                'registration' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'configureDnsSettings' => [
              'path' => 'v1/{+registration}:configureDnsSettings',
              'httpMethod' => 'POST',
              'parameters' => [
                'registration' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'configureManagementSettings' => [
              'path' => 'v1/{+registration}:configureManagementSettings',
              'httpMethod' => 'POST',
              'parameters' => [
                'registration' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'delete' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'DELETE',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'export' => [
              'path' => 'v1/{+name}:export',
              'httpMethod' => 'POST',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'get' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'GET',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'getIamPolicy' => [
              'path' => 'v1/{+resource}:getIamPolicy',
              'httpMethod' => 'GET',
              'parameters' => [
                'resource' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'options.requestedPolicyVersion' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
              ],
            ],'import' => [
              'path' => 'v1/{+parent}/registrations:import',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'initiatePushTransfer' => [
              'path' => 'v1/{+registration}:initiatePushTransfer',
              'httpMethod' => 'POST',
              'parameters' => [
                'registration' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'list' => [
              'path' => 'v1/{+parent}/registrations',
              'httpMethod' => 'GET',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'filter' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'patch' => [
              'path' => 'v1/{+name}',
              'httpMethod' => 'PATCH',
              'parameters' => [
                'name' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'updateMask' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'register' => [
              'path' => 'v1/{+parent}/registrations:register',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'renewDomain' => [
              'path' => 'v1/{+registration}:renewDomain',
              'httpMethod' => 'POST',
              'parameters' => [
                'registration' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'resetAuthorizationCode' => [
              'path' => 'v1/{+registration}:resetAuthorizationCode',
              'httpMethod' => 'POST',
              'parameters' => [
                'registration' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'retrieveAuthorizationCode' => [
              'path' => 'v1/{+registration}:retrieveAuthorizationCode',
              'httpMethod' => 'GET',
              'parameters' => [
                'registration' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'retrieveGoogleDomainsDnsRecords' => [
              'path' => 'v1/{+registration}:retrieveGoogleDomainsDnsRecords',
              'httpMethod' => 'GET',
              'parameters' => [
                'registration' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'retrieveGoogleDomainsForwardingConfig' => [
              'path' => 'v1/{+registration}:retrieveGoogleDomainsForwardingConfig',
              'httpMethod' => 'GET',
              'parameters' => [
                'registration' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'retrieveImportableDomains' => [
              'path' => 'v1/{+location}/registrations:retrieveImportableDomains',
              'httpMethod' => 'GET',
              'parameters' => [
                'location' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'pageSize' => [
                  'location' => 'query',
                  'type' => 'integer',
                ],
                'pageToken' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'retrieveRegisterParameters' => [
              'path' => 'v1/{+location}/registrations:retrieveRegisterParameters',
              'httpMethod' => 'GET',
              'parameters' => [
                'location' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'domainName' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'retrieveTransferParameters' => [
              'path' => 'v1/{+location}/registrations:retrieveTransferParameters',
              'httpMethod' => 'GET',
              'parameters' => [
                'location' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'domainName' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'searchDomains' => [
              'path' => 'v1/{+location}/registrations:searchDomains',
              'httpMethod' => 'GET',
              'parameters' => [
                'location' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
                'query' => [
                  'location' => 'query',
                  'type' => 'string',
                ],
              ],
            ],'setIamPolicy' => [
              'path' => 'v1/{+resource}:setIamPolicy',
              'httpMethod' => 'POST',
              'parameters' => [
                'resource' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'testIamPermissions' => [
              'path' => 'v1/{+resource}:testIamPermissions',
              'httpMethod' => 'POST',
              'parameters' => [
                'resource' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],'transfer' => [
              'path' => 'v1/{+parent}/registrations:transfer',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(CloudDomains::class, 'Google_Service_CloudDomains');
