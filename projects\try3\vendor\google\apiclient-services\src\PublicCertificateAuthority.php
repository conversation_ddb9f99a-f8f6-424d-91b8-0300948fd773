<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service;

use Google\Client;

/**
 * Service definition for PublicCertificateAuthority (v1).
 *
 * <p>
 * The Public Certificate Authority API may be used to create and manage ACME
 * external account binding keys associated with Google Trust Services' publicly
 * trusted certificate authority.</p>
 *
 * <p>
 * For more information about this service, see the API
 * <a href="https://cloud.google.com/public-certificate-authority/docs" target="_blank">Documentation</a>
 * </p>
 *
 * <AUTHOR> Inc.
 */
class PublicCertificateAuthority extends \Google\Service
{
  /** See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account.. */
  const CLOUD_PLATFORM =
      "https://www.googleapis.com/auth/cloud-platform";

  public $projects_locations_externalAccountKeys;
  public $rootUrlTemplate;

  /**
   * Constructs the internal representation of the PublicCertificateAuthority
   * service.
   *
   * @param Client|array $clientOrConfig The client used to deliver requests, or a
   *                                     config array to pass to a new Client instance.
   * @param string $rootUrl The root URL used for requests to the service.
   */
  public function __construct($clientOrConfig = [], $rootUrl = null)
  {
    parent::__construct($clientOrConfig);
    $this->rootUrl = $rootUrl ?: 'https://publicca.googleapis.com/';
    $this->rootUrlTemplate = $rootUrl ?: 'https://publicca.UNIVERSE_DOMAIN/';
    $this->servicePath = '';
    $this->batchPath = 'batch';
    $this->version = 'v1';
    $this->serviceName = 'publicca';

    $this->projects_locations_externalAccountKeys = new PublicCertificateAuthority\Resource\ProjectsLocationsExternalAccountKeys(
        $this,
        $this->serviceName,
        'externalAccountKeys',
        [
          'methods' => [
            'create' => [
              'path' => 'v1/{+parent}/externalAccountKeys',
              'httpMethod' => 'POST',
              'parameters' => [
                'parent' => [
                  'location' => 'path',
                  'type' => 'string',
                  'required' => true,
                ],
              ],
            ],
          ]
        ]
    );
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(PublicCertificateAuthority::class, 'Google_Service_PublicCertificateAuthority');
