<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform;

class GoogleCloudAiplatformV1GroundingSupport extends \Google\Collection
{
  protected $collection_key = 'groundingChunkIndices';
  /**
   * @var float[]
   */
  public $confidenceScores;
  /**
   * @var int[]
   */
  public $groundingChunkIndices;
  protected $segmentType = GoogleCloudAiplatformV1Segment::class;
  protected $segmentDataType = '';

  /**
   * @param float[]
   */
  public function setConfidenceScores($confidenceScores)
  {
    $this->confidenceScores = $confidenceScores;
  }
  /**
   * @return float[]
   */
  public function getConfidenceScores()
  {
    return $this->confidenceScores;
  }
  /**
   * @param int[]
   */
  public function setGroundingChunkIndices($groundingChunkIndices)
  {
    $this->groundingChunkIndices = $groundingChunkIndices;
  }
  /**
   * @return int[]
   */
  public function getGroundingChunkIndices()
  {
    return $this->groundingChunkIndices;
  }
  /**
   * @param GoogleCloudAiplatformV1Segment
   */
  public function setSegment(GoogleCloudAiplatformV1Segment $segment)
  {
    $this->segment = $segment;
  }
  /**
   * @return GoogleCloudAiplatformV1Segment
   */
  public function getSegment()
  {
    return $this->segment;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudAiplatformV1GroundingSupport::class, 'Google_Service_Aiplatform_GoogleCloudAiplatformV1GroundingSupport');
